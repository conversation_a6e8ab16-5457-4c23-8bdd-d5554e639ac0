import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/pages/order/order_detail_controller.dart';

class CouponSelect extends StatelessWidget {
  final OrderDetailController controller;

  const CouponSelect({
    super.key,
    required this.controller,
  });

  static Future<void> show(
      BuildContext context, OrderDetailController controller) {
    return showModalBottomSheet(
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CouponSelect(controller: controller),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        children: [
          // 头部
          Container(
            padding: EdgeInsets.only(top: 14.sp, bottom: 14.sp),
            child: Stack(
              children: [
                Center(
                  child: Obx(() => Text(
                        (controller.pageData['chooseCouponType'] as String) ==
                                'COUPON_TYPE_PRODUCT'
                            ? I18n.of(context)
                                    ?.translate('cm_coupon.productCoupons') ??
                                ''
                            : I18n.of(context)?.translate(
                                    'cm_coupon.commissionCoupons') ??
                                '',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      )),
                ),
                Positioned(
                  right: 13.sp,
                  top: 2.sp,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      controller.onChooseCouponCancel();
                    },
                    child: Icon(
                      Icons.close,
                      size: 20.sp,
                      color: const Color(0xFF222222),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 优惠券列表
          Expanded(
            child: Obx(() => (controller.pageData['chooseCouponLoading']
                        as bool? ??
                    false)
                ? const Center(
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                    ),
                  )
                : ListView.builder(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.sp, vertical: 10.sp),
                    itemCount: (controller.pageData['chooseCouponList'] as List)
                        .length,
                    itemBuilder: (context, index) {
                      final coupon = (controller.pageData['chooseCouponList']
                          as List)[index];
                      return Obx(() {
                        final isLocked =
                            coupon['ticketStatus'] == 'TICKET_LOCK';
                        final isDisabled = coupon['availableFlag'] == false;
                        // 锁定时始终视为选中
                        final isChecked =
                            (controller.pageData['selectedCouponIds']
                                        as List<String>)
                                    .contains(coupon['id']) ||
                                isLocked;

                        return Container(
                          margin: EdgeInsets.only(bottom: 10.sp),
                          child: Column(
                            children: [
                              GestureDetector(
                                onTap: isDisabled || isLocked
                                    ? null
                                    : () {
                                        final selectedIds = controller
                                                .pageData['selectedCouponIds']
                                            as List<String>;
                                        if (isChecked) {
                                          selectedIds.remove(coupon['id']);
                                        } else {
                                          selectedIds.add(coupon['id']);
                                        }
                                        controller.onCheckAvailableList();
                                      },
                                child: Container(
                                  padding: EdgeInsets.only(bottom: 10.sp),
                                  child: Row(
                                    children: [
                                      DottedBorder(
                                        color: isDisabled
                                            ? const Color(0xFF7F7F7F)
                                            : isChecked || isLocked
                                                ? AppColors.primaryColor
                                                : const Color(0xFF333333),
                                        strokeWidth: 1.5.sp,
                                        borderType: BorderType.RRect,
                                        radius: Radius.circular(4.sp),
                                        padding: EdgeInsets.zero,
                                        child: Container(
                                          width: 78.sp,
                                          padding: EdgeInsets.symmetric(
                                              vertical: 14.sp),
                                          alignment: Alignment.center,
                                          child: Text(
                                            coupon['couponWay'] ==
                                                    'COUPON_WAY_DISCOUNT'
                                                ? '${discountToPercentage(coupon['discount'])} ${I18n.of(context)?.translate('cm_coupon.discount')?.toLowerCase() ?? ''}'
                                                : setNewUnit(coupon[
                                                    'preferentialAmount']),
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              color: isDisabled
                                                  ? const Color(0xFF7F7F7F)
                                                  : isChecked || isLocked
                                                      ? AppColors.primaryColor
                                                      : const Color(0xFF333333),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 7.w),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              _getCouponConditionText(
                                                  context, coupon),
                                              style: TextStyle(
                                                fontSize: 13.sp,
                                                color: isDisabled
                                                    ? const Color(0xFF7F7F7F)
                                                    : isChecked || isLocked
                                                        ? const Color(
                                                            0xFFE50113)
                                                        : const Color(
                                                            0xFF333333),
                                              ),
                                            ),
                                            if (coupon['couponWay'] ==
                                                    'COUPON_WAY_DISCOUNT' &&
                                                coupon['preferentialAmount'] !=
                                                    null &&
                                                coupon['preferentialAmount'] >
                                                    0)
                                              Padding(
                                                padding:
                                                    EdgeInsets.only(top: 7.h),
                                                child: Text(
                                                  '${I18n.of(context)?.translate('cm_coupon.upToMoney')} ${setNewUnit(coupon['preferentialAmount'])}',
                                                  style: TextStyle(
                                                    fontSize: 12.sp,
                                                    color:
                                                        const Color(0xFF666666),
                                                  ),
                                                ),
                                              ),
                                            if (isDisabled &&
                                                coupon['notAvailableReason'] !=
                                                    null)
                                              Padding(
                                                padding:
                                                    EdgeInsets.only(top: 7.sp),
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Align(
                                                      alignment:
                                                          Alignment.topCenter,
                                                      child: Icon(
                                                        Icons.info,
                                                        size: 12.sp,
                                                        color: const Color(
                                                            0xFF999999),
                                                      ),
                                                    ),
                                                    SizedBox(width: 2.sp),
                                                    Expanded(
                                                      child: Text(
                                                        coupon[
                                                            'notAvailableReason'],
                                                        style: TextStyle(
                                                          height: 1,
                                                          fontSize: 11.sp,
                                                          color: const Color(
                                                              0xFF999999),
                                                        ),
                                                        softWrap: true,
                                                        overflow: TextOverflow
                                                            .visible,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                      // 优惠券选择圆圈：不可用时置灰且不显示勾选
                                      Container(
                                        width: 20.sp,
                                        height: 20.sp,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: isDisabled
                                                ? const Color(0xFFCCCCCC)
                                                : (isChecked || isLocked)
                                                    ? AppColors.primaryColor
                                                    : const Color(0xFFCCCCCC),
                                            width: 1,
                                          ),
                                          color: isDisabled
                                              ? const Color(0xFFF5F5F5)
                                              : (isChecked || isLocked)
                                                  ? AppColors.primaryColor
                                                  : Colors.white,
                                        ),
                                        child: (!isDisabled &&
                                                (isChecked || isLocked))
                                            ? Icon(
                                                Icons.check,
                                                size: 16.sp,
                                                color: Colors.white,
                                              )
                                            : null,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (index <
                                  (controller.pageData['chooseCouponList']
                                              as List)
                                          .length -
                                      1)
                                Divider(
                                  height: 1,
                                  color: const Color(0xFFF2F2F2),
                                ),
                            ],
                          ),
                        );
                      });
                    },
                  )),
          ),
          // 底部操作栏
          Container(
            padding: EdgeInsets.all(8.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.1),
                  offset: const Offset(0, -1),
                  blurRadius: 4,
                ),
              ],
            ),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.sp),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        I18n.of(context)
                                ?.translate('cm_order.existingDiscount') ??
                            '',
                        style: TextStyle(
                          fontSize: 14.sp,
                        ),
                      ),
                      Obx(() => Text(
                            setUnit((controller.pageData['chooseCouponAmount']
                                    as num?) ??
                                0),
                            style: TextStyle(
                              fontSize: 18.sp,
                              color: AppColors.primaryColor,
                            ),
                          )),
                    ],
                  ),
                ),
                SizedBox(height: 12.sp),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                          controller.onChooseCouponCancel();
                        },
                        child: Container(
                          height: 40.sp,
                          margin: EdgeInsets.only(right: 12.sp),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF6D2D4),
                            borderRadius: BorderRadius.circular(20.sp),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            I18n.of(context)
                                    ?.translate('cm_order.orderCancel') ??
                                '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          controller.onChooseCouponConfirm();
                          Navigator.pop(context);
                        },
                        child: Container(
                          height: 40.sp,
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(20.sp),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            I18n.of(context)
                                    ?.translate('cm_order.orderConfirm') ??
                                '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getCouponConditionText(BuildContext context, dynamic coupon) {
    switch (coupon['couponUseConditionsType']) {
      case 'FULL':
        return '${I18n.of(context)?.translate('cm_coupon.minimumRequired')} ${setNewUnit(coupon['useConditionsAmount'])}';
      case 'EVERY_FULL':
        return '${I18n.of(context)?.translate('cm_coupon.minimumUnmet')} ${setNewUnit(coupon['useConditionsAmount'])} ${I18n.of(context)?.translate('cm_coupon.minimumUnmetCost')}';
      case 'UNLIMITED':
        return I18n.of(context)?.translate('cm_coupon.noLimit') ?? '';
      default:
        return '';
    }
  }
}
