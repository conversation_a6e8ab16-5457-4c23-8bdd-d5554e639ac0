import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_step_progress.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/find/components/coupon_card.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SubmitThankyou extends StatefulWidget {
  final String? email;
  final String? whatsapp;
  final bool? firstSubmit;

  const SubmitThankyou(
      {super.key, this.email, this.whatsapp, this.firstSubmit = false});

  @override
  State<SubmitThankyou> createState() => _SubmitThankyouState();
}

class _SubmitThankyouState extends State<SubmitThankyou> {
  bool _isLoading = false;
  double screenWidth = 0.0;
  final dynamic _pageData = {};
  AuthLoginModel? user;

  @override
  void initState() {
    super.initState();

    _pageData["email"] = Get.arguments["email"] ?? "";
    _pageData["whatsapp"] = Get.arguments["whatsapp"] ?? "";
    _pageData["firstSubmit"] = Get.arguments["firstSubmit"] ?? false;
    _pageData['isMailVerified'] = false;
    onPageData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 查询列表页数据
  Future<void> onPageData({String type = ''}) async {
    try {
      setState(() {
        _isLoading = true;
      });
      AuthLoginModel? userInfo = await Global.getUserInfo();

      if (widget.firstSubmit == true) {
        dynamic res = await ProductAPI.useQueryVerifyMailResult({
          "email": userInfo?.username ?? "",
          "isNeedCoupon": true,
          "verifyMailScene": "FIRST_GOODS_LOOKING",
        });
        print('useQueryVerifyMailResult:$res');
        if (res != null && res?['result']?['code'] == 200) {
          setState(() {
            user = userInfo;
            _pageData['couponList'] = res?['data']?['couponList'];
            _pageData['isMailVerified'] =
                res?['data']?['isMailVerified'] ?? false;
            _pageData['expireHour'] = res?['data']?['expireHour'] ?? 0;

            if (type == "verifyMail") {
              if (res?['data']?['isMailVerified'] == true) {
                _pageData['isMailVerified'] = true;
              } else {
                navigateToEmail();
              }
            }
          });
        }
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> onResendVerification() async {
    try {
      final res = await UserAPI.useSendVerifyMail({
        "verifyMailScene": "FIRST_GOODS_LOOKING",
      });
      if (res['result']['code'] == 200) {
        if (res['data']?['isMailVerified'] == true) {
          NavigatorUtil.pushNamed(context, AppRoutes.MineCouponPage);
        } else {
          showErrorMessage(
              I18n.of(context)!.translate('cm_common.resendSuccess'));
        }
      } else {
        showErrorMessage(res['result']?['message'] ??
            I18n.of(context)!.translate('cm_common.errorMessage'));
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;

    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            onPopInvoked: (result) {
              NavigatorUtil.pushNamed(context, AppRoutes.CartPage);
            },
            body: Column(children: [
              SearchHeader(showHomeIcon: true),
              Expanded(
                child: SingleChildScrollView(
                  child: _buildThankYouContent(),
                ),
              )
            ]));
  }

  // 感谢内容
  Widget _buildThankYouContent() {
    return Column(
      children: [
        OrderProgressBar(
          currentStep: 2,
        ),
        Container(
          alignment: Alignment.center,
          padding: EdgeInsets.only(top: 12.sp),
          child: Text(
            I18n.of(context)!.translate('cm_common.step3'),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Container(
          height: 1, // 分割线粗细
          color: Colors.grey.shade200, // 分割线颜色
          margin: EdgeInsets.only(top: 12.sp),
        ),
        Container(
          alignment: Alignment.center,
          padding: EdgeInsets.only(top: 12.sp),
          child: SvgPicture.asset(
            'assets/images/common/feedback_success.svg',
            width: 48.sp,
          ),
        ),
        Container(
            alignment: Alignment.center,
            padding: EdgeInsets.only(top: 10.sp),
            child: Text(
              I18n.of(context)!.translate('cm_search.sentSuccess'),
              style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w500),
            )),
        Container(
            alignment: Alignment.center,
            padding: EdgeInsets.only(top: 4.sp),
            child: Text(
              I18n.of(context)!.translate('cm_find_submitSuccessTip'),
            )),
        Container(
            alignment: Alignment.center,
            padding: EdgeInsets.only(top: 4.sp),
            child: Text(
              "WhatsApp!",
              style: TextStyle(
                  fontSize: 24.sp,
                  color: Colors.green.shade500,
                  fontWeight: FontWeight.w500),
            )),
        Container(
          padding: EdgeInsets.only(top: 4.sp, left: 56.sp, right: 56.sp),
          child: Text.rich(TextSpan(
            children: [
              TextSpan(
                text: I18n.of(context)!.translate('cm_search.successEmail'),
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              TextSpan(
                text: ' ${_pageData['email'] ?? ''}',
                style: TextStyle(fontSize: 16, color: AppColors.primaryColor),
              ),
            ],
          )),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 10.sp),
          child: Text.rich(TextSpan(
            children: [
              TextSpan(
                text: I18n.of(context)!.translate('cm_search.orderShipped'),
                style: TextStyle(fontSize: 14.sp, color: Colors.black54),
              ),
              TextSpan(
                text: '(${_pageData['whatsapp'] ?? ''}) ',
                style: TextStyle(fontSize: 14.sp, color: Colors.black54),
              ),
              TextSpan(
                text: I18n.of(context)!.translate('cm_search.shippingQuote'),
                style: TextStyle(fontSize: 14.sp, color: Colors.black54),
              ),
            ],
          )),
        ),
        Visibility(
            visible: !_pageData["firstSubmit"],
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 12.sp),
              child: Center(
                child: FancyButton(
                  onTap: () {
                    NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
                  },
                  width: screenWidth * 0.65,
                  color: AppColors.primaryColor,
                  borderColor: Colors.grey.shade300,
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.sp, vertical: 6.sp),
                  borderRadius: BorderRadius.circular(24.0.sp),
                  child: Text(
                    I18n.of(context)!.translate('cm_search.goToHome'),
                    style: TextStyle(fontSize: 16, color: Colors.white),
                  ),
                ),
              ),
            )),
        Visibility(
          visible: !_pageData["isMailVerified"] &&
              (_pageData['couponList']?.length ?? 0) > 0,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 12.sp, horizontal: 12.sp),
            decoration: BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.only(top: 12.sp, bottom: 16.sp),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12.sp),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 12.sp, horizontal: 12.sp),
                        child: Text(
                          I18n.of(context)!
                              .translate('cm_common.activationReward'),
                        ),
                      ),
                      Wrap(
                          spacing: 6.0.sp, // 水平间距
                          runSpacing: 12.0.sp, // 垂直间距
                          children: List.generate(
                              (_pageData['couponList'] ?? []).length, (index) {
                            return CouponCard(
                                coupon: _pageData['couponList']?[index]);
                          })),
                    ],
                  ),
                ),
                Container(
                  padding:
                      EdgeInsets.only(left: 12.sp, right: 12.sp, top: 24.sp),
                  child: Text.rich(
                      style: TextStyle(color: Colors.black54),
                      TextSpan(children: [
                        TextSpan(
                          text: I18n.of(context)!
                              .translate('cm_common.activationEmail'),
                        ),
                        TextSpan(
                          text: user?.username ?? "-----",
                        ),
                        TextSpan(
                          text: I18n.of(context)!
                              .translate('cm_common.activationValidTime'),
                        ),
                        TextSpan(
                            text: _pageData['expireHour']?.toString(),
                            style: TextStyle(
                              color: AppColors.primaryColor,
                            )),
                        TextSpan(
                            text: (_pageData['expireHour'] ?? 0) > 1
                                ? I18n.of(context)!
                                    .translate('cm_common.activationTimeUnits')
                                : (_pageData['expireHour'] ?? 0) == 1
                                    ? I18n.of(context)!.translate(
                                        'cm_common.activationTimeUnit')
                                    : '',
                            style: TextStyle(
                              color: AppColors.primaryColor,
                            )),
                        TextSpan(
                            text: ".",
                            style: TextStyle(
                              color: AppColors.primaryColor,
                            )),
                      ])),
                ),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.sp, vertical: 12.sp),
                  child: FancyButton(
                    onTap: () {
                      onPageData(type: "verifyMail");
                    },
                    width: screenWidth * 0.95,
                    color: AppColors.primaryColor,
                    borderColor: Colors.grey.shade300,
                    padding: EdgeInsets.symmetric(
                        horizontal: 12.sp, vertical: 10.sp),
                    borderRadius: BorderRadius.circular(24.0.sp),
                    child: Text(
                      I18n.of(context)!.translate('cm_common_emailActivate'),
                      style: TextStyle(
                          color: Colors.white, fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.sp),
                  child: Text(
                      I18n.of(context)!
                          .translate('cm_common.verificationEmail'),
                      style: TextStyle(
                          fontSize: 18.sp,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500)),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 18.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.only(top: 10.sp),
                        child: Text(
                          '• ${I18n.of(context)!.translate('cm_common.spamCheck')}',
                          style: TextStyle(height: 1.2.sp),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 8.sp),
                        child: Text(
                          '• ${I18n.of(context)!.translate('cm_common.deliveryDelay')}',
                          style: TextStyle(height: 1.2.sp),
                        ),
                      ),
                      RichText(
                        text: TextSpan(
                            style:
                                TextStyle(color: Colors.black, height: 1.2.sp),
                            children: [
                              TextSpan(
                                  text:
                                      '• ${I18n.of(context)!.translate('cm_common.verificationDelay')}'),
                              TextSpan(
                                text:
                                    ' ${I18n.of(context)!.translate('cm_common.resendVerification')}',
                                style: TextStyle(
                                  color: Colors.blue.shade400,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // print('官方文档被点击');
                                    // 导航到网页
                                    onResendVerification();
                                  },
                              ),
                            ]),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
        Visibility(
          visible: _pageData["isMailVerified"] &&
              (_pageData['couponList']?.length ?? 0) > 0,
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.symmetric(horizontal: 12.sp),
                padding: EdgeInsets.only(top: 12.sp, bottom: 16.sp),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12.sp),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                          vertical: 12.sp, horizontal: 12.sp),
                      child: Text(
                        I18n.of(context)!
                            .translate('cm_common.activationReward'),
                      ),
                    ),
                    Wrap(
                        spacing: 6.0.sp, // 水平间距
                        runSpacing: 12.0.sp, // 垂直间距
                        children: List.generate(
                            (_pageData['couponList'] ?? []).length, (index) {
                          return CouponCard(
                              coupon: _pageData['couponList']?[index]);
                        })),
                  ],
                ),
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(horizontal: 12.sp, vertical: 12.sp),
                padding: EdgeInsets.symmetric(vertical: 12.sp),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12.sp),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 12.sp, vertical: 12.sp),
                      child: Text(
                        I18n.of(context)!
                            .translate('cm_common.issuedToAccount'),
                        style:
                            TextStyle(fontSize: 16.sp, color: Colors.black87),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        NavigatorUtil.pushNamed(
                            context, AppRoutes.MineCouponPage);
                      },
                      child: Text(
                        I18n.of(context)!.translate('cm_common.viewInCoupons'),
                        style: TextStyle(
                            fontSize: 16.sp, color: AppColors.primaryColor),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
        SizedBox(
          height: 100.sp,
        )
      ],
    );
  }
}
